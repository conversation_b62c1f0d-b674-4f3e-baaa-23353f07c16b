# Next.js 15 MVP 全栈开发 AI 指导规则

基于最新技术最佳实践，专为快速高效的 MVP 开发而设计的 AI 助手指导规则。

## 核心开发理念

你是 TypeScript、Next.js 15、React 19、Shadcn UI、Tailwind CSS v4、Prisma、PostgreSQL、TanStack Query v5、Zod、pnpm 和 Clerk 的专家。

### 开发原则

- **MVP 优先**：快速迭代，保持必要的可扩展性，避免过度工程化
- **严格遵循 SOLID、KISS、DRY、YAGNI 原则**
- **函数式和声明式编程模式**优于命令式
- **组件驱动开发**，注重组合和复用
- **类型安全**贯穿整个技术栈
- **性能优先**，但不牺牲开发效率

## 代码风格与结构

### 通用准则

- 编写简洁的 TypeScript 代码，提供准确示例
- 使用函数式和声明式编程模式，避免类
- 优先迭代和模块化，避免代码重复
- 使用描述性变量名，带辅助动词（如 isLoading、hasError）
- 文件结构：导出组件、子组件、工具函数、静态内容、类型
- 使用 tabs 缩进，字符串用单引号
- 省略分号，除非消歧需要
- 始终使用严格相等（===）而非宽松相等（==）
- 行长度限制在 80 字符内

### 命名规范

- **组件、类型定义、接口**：PascalCase
- **目录**：小写连字符（如 components/auth-wizard）
- **文件名**：kebab-case（如 user-profile.tsx）
- **变量、函数、方法、Hooks、属性**：camelCase
- **环境变量、常量、全局配置**：UPPERCASE
- **事件处理器**前缀 'handle'：handleClick、handleSubmit
- **布尔变量**前缀动词：isLoading、hasError、canSubmit
- **自定义 Hooks** 前缀 'use'：useAuth、useApiCall
- **优先使用命名导出**

## TypeScript 最佳实践

### 核心实现

- 所有代码使用 TypeScript，**接口优于类型**
- 启用 tsconfig.json 中的**严格模式**
- **避免枚举**，使用 const 对象或联合类型
- 使用**类型守卫**安全处理 undefined 或 null 值
- 应用**泛型**提供类型灵活性
- 利用 TypeScript **工具类型**（Partial、Pick、Omit、Record）
- 使用**映射类型**动态创建现有类型的变体

```typescript
// 推荐：接口用于对象结构
interface UserProps {
  id: string;
  email: string;
  name: string;
}

// 推荐：const 对象替代枚举
const UserRole = {
  ADMIN: 'admin',
  USER: 'user',
  GUEST: 'guest',
} as const;

type UserRoleType = (typeof UserRole)[keyof typeof UserRole];
```

## Next.js 15 最佳实践

### 核心架构

- **专门使用 App Router**，避免 Pages Router
- **默认使用 Server Components** 提升性能
- **谨慎使用 'use client'**，仅在必要时：
  - 事件监听器和浏览器 API
  - 状态管理 hooks（useState、useReducer）
  - 仅客户端库
  - 需要用户输入的交互组件
- 使用 **generateMetadata** 实现适当的元数据管理
- 使用**路由组**实现基于文件的路由

### 性能优化

- **最小化** 'use client'、'useEffect' 和 'setState' 使用
- 优先使用 **React Server Components**
- 用 **Suspense** 包装客户端组件，提供回退 UI
- 对非关键组件使用**动态加载**
- 优化图像：使用 **Next.js Image** 组件、WebP 格式、懒加载
- 实施适当的**缓存策略**和重新验证
- 利用 Next.js 15 改进的**缓存机制**

### 新特性应用

- 利用 **React 19 支持**和新的 React 编译器
- 使用**部分预渲染（PPR）**结合 SSG 和 SSR
- 应用 **TurboPack 集成**提升开发速度

## React 19 最佳实践

### 组件架构

- 使用带 TypeScript 接口的**函数组件**
- 使用 **function 关键字**定义纯函数组件
- 将可重用逻辑提取到**自定义 hooks**
- 实现适当的**组件组合模式**
- 战略性使用 **React.memo()** 进行性能优化
- 在 useEffect hooks 中实施适当的**清理**

```typescript
interface ButtonProps {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
}

function Button({
  variant = 'primary',
  size = 'md',
  children,
  onClick,
  disabled = false,
}: ButtonProps) {
  return (
    <button
      className={cn(buttonVariants({ variant, size }))}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}
```

## Shadcn UI + Tailwind CSS v4 最佳实践

### 设计系统集成

- **严格使用项目提供的 CSS 变量**，不自定义颜色和圆角
- 所有组件必须使用 `index.css` 中定义的设计令牌：
  - 颜色：`--primary`、`--secondary`、`--muted` 等
  - 圆角：`--radius-sm`、`--radius-md`、`--radius-lg` 等
  - 阴影：`--shadow-sm`、`--shadow-md`、`--shadow-lg` 等
  - 字体：`--font-sans`、`--font-mono`、`--font-serif`

### 组件开发规范

- 使用 **Shadcn UI 组件**作为一致设计的基础
- 实施 **Radix UI 原语**以获得高级可访问性功能
- 应用**组合模式**创建模块化、可重用的组件
- 使用 **Tailwind 工具类**遵循移动优先的响应式设计
- 逻辑分组 Tailwind 类：布局、间距、颜色、排版
- 使用 **cn() 工具函数**进行条件类合并
- 使用 **next-themes** 实施暗模式支持

```typescript
// 正确：使用设计令牌
<div className="bg-primary text-primary-foreground rounded-lg shadow-md">
  Content
</div>

// 错误：自定义颜色和样式
<div className="bg-blue-500 text-white rounded-xl shadow-2xl">
  Content
</div>
```

### 响应式设计

- 使用 Tailwind 断点设计**移动优先**方法
- 确保可访问性合规的适当**对比度比**
- 使用**语义 HTML 元素**以获得更好的屏幕阅读器支持
- 为键盘导航实施适当的**焦点管理**

## 现代 UI 设计指导原则

### 视觉层次和布局

#### 现代网格系统和布局策略

- **CSS Grid 优先**：使用 CSS Grid 处理二维布局，Flexbox 处理一维布局
- **Container Queries**：利用现代 CSS 容器查询实现真正的组件级响应式设计
- **逻辑属性**：使用 `margin-inline`、`padding-block` 等逻辑属性支持国际化

```typescript
// 现代响应式网格组件
interface GridLayoutProps {
  children: React.ReactNode;
  columns?: 'auto' | number;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

function GridLayout({
  children,
  columns = 'auto',
  gap = 'md',
  className,
}: GridLayoutProps) {
  const gridCols =
    columns === 'auto'
      ? 'grid-cols-[repeat(auto-fit,minmax(280px,1fr))]'
      : `grid-cols-${columns}`;

  return (
    <div
      className={cn(
        'grid',
        gridCols,
        `gap-${gap}`,
        'container-type-inline-size', // Container queries support
        className
      )}
    >
      {children}
    </div>
  );
}
```

#### 空白空间（Whitespace）的有效利用

- **8px 网格系统**：所有间距基于 8px 的倍数（4px、8px、16px、24px、32px）
- **垂直节奏**：保持一致的垂直间距，使用 `space-y-*` 工具类
- **内容密度平衡**：避免过于拥挤或过于稀疏的布局

```typescript
// 标准间距组件
function ContentSection({
  title,
  children,
  density = 'normal',
}: {
  title: string;
  children: React.ReactNode;
  density?: 'compact' | 'normal' | 'spacious';
}) {
  const spacingMap = {
    compact: 'space-y-4',
    normal: 'space-y-6',
    spacious: 'space-y-8',
  };

  return (
    <section className={cn('w-full', spacingMap[density])}>
      <h2 className="text-2xl font-semibold text-foreground mb-4">{title}</h2>
      <div className={spacingMap[density]}>{children}</div>
    </section>
  );
}
```

#### 视觉权重和焦点引导

- **Z-index 层级管理**：建立清晰的层级系统（content: 1, overlay: 10, modal: 50, toast: 100）
- **视觉焦点**：使用颜色、大小、对比度引导用户注意力
- **信息架构**：遵循 F 型或 Z 型视觉扫描模式

### 色彩和对比度

#### 现代色彩理论应用

- **60-30-10 规则**：主色调 60%，次要色调 30%，强调色 10%
- **色彩心理学**：根据品牌和功能选择合适的色彩情感表达
- **渐变和阴影**：使用微妙的渐变和阴影增加深度感

```typescript
// 色彩工具函数
const colorUtils = {
  // 获取对比色
  getContrastColor: (bgColor: string) => {
    // 使用项目 CSS 变量确保一致性
    const colorMap = {
      'bg-primary': 'text-primary-foreground',
      'bg-secondary': 'text-secondary-foreground',
      'bg-muted': 'text-muted-foreground',
      'bg-card': 'text-card-foreground',
    };
    return colorMap[bgColor as keyof typeof colorMap] || 'text-foreground';
  },
};

// 状态色彩组件
function StatusBadge({
  status,
  children,
}: {
  status: 'success' | 'warning' | 'error' | 'info';
  children: React.ReactNode;
}) {
  const statusStyles = {
    success: 'bg-green-100 text-green-800 border-green-200',
    warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    error: 'bg-destructive/10 text-destructive border-destructive/20',
    info: 'bg-blue-100 text-blue-800 border-blue-200',
  };

  return (
    <span
      className={cn(
        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border',
        statusStyles[status]
      )}
    >
      {children}
    </span>
  );
}
```

#### WCAG 2.2 无障碍设计合规

- **对比度要求**：
  - 正常文本：最低 4.5:1（AA 级），推荐 7:1（AAA 级）
  - 大文本（18pt+ 或 14pt+ 粗体）：最低 3:1（AA 级），推荐 4.5:1（AAA 级）
  - 非文本元素：最低 3:1
- **色彩独立性**：不仅依赖颜色传达信息，结合图标、文字、形状
- **焦点指示器**：确保键盘导航时有清晰的焦点指示

```typescript
// 无障碍按钮组件
function AccessibleButton({
  variant = 'primary',
  size = 'md',
  children,
  ariaLabel,
  ...props
}: ButtonProps & { ariaLabel?: string }) {
  return (
    <button
      className={cn(
        // 基础样式
        'inline-flex items-center justify-center rounded-md font-medium transition-colors',
        // 焦点样式 - 确保高对比度
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
        // 禁用状态
        'disabled:pointer-events-none disabled:opacity-50',
        // 变体样式
        buttonVariants({ variant, size })
      )}
      aria-label={ariaLabel}
      {...props}
    >
      {children}
    </button>
  );
}
```

#### 暗模式和亮模式策略

- **系统偏好检测**：使用 `prefers-color-scheme` 媒体查询
- **用户选择优先**：允许用户覆盖系统设置
- **平滑过渡**：使用 CSS 过渡实现模式切换动画

```typescript
// 主题切换组件
function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  return (
    <button
      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
      className={cn(
        'relative inline-flex h-9 w-16 items-center rounded-full transition-colors',
        'bg-muted hover:bg-muted/80',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring'
      )}
      aria-label="切换主题"
    >
      <span
        className={cn(
          'inline-block h-7 w-7 transform rounded-full bg-background shadow-lg transition-transform',
          theme === 'dark' ? 'translate-x-8' : 'translate-x-1'
        )}
      >
        {theme === 'light' ? (
          <Sun className="h-4 w-4 m-1.5" />
        ) : (
          <Moon className="h-4 w-4 m-1.5" />
        )}
      </span>
    </button>
  );
}
```

### 字体排版

#### 现代 Web 字体系统

- **字体层级**：建立清晰的标题层级（H1-H6）和正文层级
- **字体加载优化**：使用 `font-display: swap` 和字体预加载
- **可变字体**：利用可变字体技术减少文件大小

```typescript
// 排版组件系统
interface TypographyProps {
  variant: 'h1' | 'h2' | 'h3' | 'h4' | 'body' | 'caption' | 'overline';
  children: React.ReactNode;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

function Typography({ variant, children, className, as }: TypographyProps) {
  const Component = as || getDefaultElement(variant);

  const variants = {
    h1: 'text-4xl font-bold tracking-tight lg:text-5xl',
    h2: 'text-3xl font-semibold tracking-tight',
    h3: 'text-2xl font-semibold tracking-tight',
    h4: 'text-xl font-semibold tracking-tight',
    body: 'text-base leading-7',
    caption: 'text-sm text-muted-foreground',
    overline:
      'text-xs font-medium uppercase tracking-wider text-muted-foreground',
  };

  return (
    <Component className={cn(variants[variant], className)}>
      {children}
    </Component>
  );
}
```

#### 行高和字间距最佳实践

- **行高比例**：正文 1.5-1.6，标题 1.2-1.4
- **字符间距**：标题适当增加 `tracking-tight`，正文保持默认
- **段落间距**：使用 `space-y-*` 保持一致的段落间距

#### 多语言支持策略

- **字体回退**：为不同语言设置合适的字体栈
- **文本方向**：支持 RTL 语言的布局
- **字符编码**：确保正确的 UTF-8 编码

### 微交互和动画

#### Motion.dev 集成最佳实践

- **性能优先**：优先使用 `transform` 和 `opacity` 属性
- **有意义的动画**：每个动画都应有明确的用户体验目的
- **时长控制**：微交互 200-300ms，页面转换 300-500ms

```typescript
import { motion } from 'motion/react';

// 微交互组件
function InteractiveCard({
  children,
  onClick,
}: {
  children: React.ReactNode;
  onClick?: () => void;
}) {
  return (
    <motion.div
      className="bg-card border rounded-lg p-6 cursor-pointer"
      whileHover={{
        scale: 1.02,
        boxShadow: 'var(--shadow-lg)',
      }}
      whileTap={{ scale: 0.98 }}
      transition={{
        type: 'spring',
        stiffness: 400,
        damping: 17,
      }}
      onClick={onClick}
    >
      {children}
    </motion.div>
  );
}

// 页面转换动画
function PageTransition({ children }: { children: React.ReactNode }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{
        duration: 0.3,
        ease: [0.4, 0.0, 0.2, 1], // Custom easing
      }}
    >
      {children}
    </motion.div>
  );
}
```

#### 减少动画偏好支持

- **媒体查询检测**：使用 `prefers-reduced-motion` 媒体查询
- **替代方案**：为动画敏感用户提供静态替代方案
- **用户控制**：提供动画开关选项

```typescript
// 响应式动画组件
function ResponsiveMotion({
  children,
  animation = true,
}: {
  children: React.ReactNode;
  animation?: boolean;
}) {
  return (
    <motion.div
      initial={animation ? { opacity: 0, scale: 0.95 } : false}
      animate={animation ? { opacity: 1, scale: 1 } : false}
      transition={{
        duration: 0.2,
        ease: 'easeOut',
      }}
      // 响应用户偏好
      style={{
        '@media (prefers-reduced-motion: reduce)': {
          animation: 'none',
          transition: 'none',
        },
      }}
    >
      {children}
    </motion.div>
  );
}
```

### 响应式设计现代规范

#### 移动优先设计策略

- **断点系统**：使用 Tailwind CSS 的现代断点策略
  - `sm`: 640px（小型平板）
  - `md`: 768px（平板）
  - `lg`: 1024px（笔记本电脑）
  - `xl`: 1280px（桌面）
  - `2xl`: 1536px（大屏幕）

```typescript
// 响应式容器组件
function ResponsiveContainer({
  children,
  maxWidth = '7xl',
}: {
  children: React.ReactNode;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '7xl';
}) {
  return (
    <div className={cn('mx-auto px-4 sm:px-6 lg:px-8', `max-w-${maxWidth}`)}>
      {children}
    </div>
  );
}

// 响应式网格布局
function ResponsiveGrid({ children }: { children: React.ReactNode }) {
  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {children}
    </div>
  );
}
```

#### 现代断点管理

- **容器查询**：使用 `@container` 查询实现组件级响应式
- **折叠屏支持**：考虑折叠屏设备的特殊断点
- **高密度屏幕**：使用 `@media (min-resolution: 2dppx)` 优化高 DPI 显示

```typescript
// 自适应卡片组件
function AdaptiveCard({
  title,
  content,
  actions,
}: {
  title: string;
  content: React.ReactNode;
  actions?: React.ReactNode;
}) {
  return (
    <div
      className={cn(
        // 基础样式
        'bg-card border rounded-lg shadow-sm',
        // 响应式内边距
        'p-4 sm:p-6',
        // 容器查询支持
        '@container',
        // 在小容器中调整布局
        '@[300px]:p-4 @[500px]:p-6'
      )}
    >
      <h3 className="text-lg font-semibold mb-3 @[300px]:text-xl">{title}</h3>
      <div className="mb-4 @[400px]:mb-6">{content}</div>
      {actions && (
        <div className="flex flex-col gap-2 @[400px]:flex-row @[400px]:justify-end">
          {actions}
        </div>
      )}
    </div>
  );
}
```

#### 触摸友好界面设计

- **最小触摸目标**：确保所有可交互元素至少 44px × 44px
- **手势支持**：实现滑动、捏合等现代手势
- **触摸反馈**：提供即时的视觉和触觉反馈

```typescript
// 触摸友好按钮
function TouchFriendlyButton({
  children,
  size = 'default',
  ...props
}: ButtonProps) {
  const sizeClasses = {
    sm: 'h-9 px-3 text-sm', // 36px height, still accessible
    default: 'h-11 px-4', // 44px height - optimal touch target
    lg: 'h-12 px-6 text-lg', // 48px height - comfortable for large fingers
  };

  return (
    <button
      className={cn(
        'inline-flex items-center justify-center rounded-md font-medium',
        'transition-all duration-200',
        // 触摸状态样式
        'active:scale-95 active:brightness-95',
        // 确保足够的触摸目标
        'min-w-[44px]',
        sizeClasses[size]
      )}
      {...props}
    >
      {children}
    </button>
  );
}

// 滑动手势组件
function SwipeableCard({
  children,
  onSwipeLeft,
  onSwipeRight,
}: {
  children: React.ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
}) {
  return (
    <motion.div
      className="bg-card border rounded-lg p-4"
      drag="x"
      dragConstraints={{ left: -100, right: 100 }}
      dragElastic={0.2}
      onDragEnd={(_, info) => {
        if (info.offset.x > 100 && onSwipeRight) {
          onSwipeRight();
        } else if (info.offset.x < -100 && onSwipeLeft) {
          onSwipeLeft();
        }
      }}
      whileDrag={{ scale: 1.05 }}
    >
      {children}
    </motion.div>
  );
}
```

#### 跨设备一致性保证

- **视觉一致性**：确保核心品牌元素在所有设备上保持一致
- **功能对等**：移动端和桌面端提供相同的核心功能
- **性能平衡**：根据设备能力调整功能复杂度

### 技术栈集成指导

#### CSS 变量系统充分利用

- **动态主题**：使用 CSS 变量实现运行时主题切换
- **组件变体**：基于 CSS 变量创建组件变体系统
- **响应式值**：使用 CSS 变量实现响应式设计值

```typescript
// 动态主题组件
function DynamicThemeProvider({
  children,
  customTheme,
}: {
  children: React.ReactNode;
  customTheme?: Record<string, string>;
}) {
  const themeVars = customTheme
    ? {
        '--primary': customTheme.primary || 'var(--primary)',
        '--secondary': customTheme.secondary || 'var(--secondary)',
        '--accent': customTheme.accent || 'var(--accent)',
      }
    : {};

  return (
    <div
      style={themeVars}
      className="min-h-screen bg-background text-foreground"
    >
      {children}
    </div>
  );
}

// 基于 CSS 变量的组件变体
function VariantCard({
  variant = 'default',
  children,
}: {
  variant?: 'default' | 'primary' | 'secondary';
  children: React.ReactNode;
}) {
  const variantStyles = {
    default: {
      '--card-bg': 'var(--card)',
      '--card-text': 'var(--card-foreground)',
      '--card-border': 'var(--border)',
    },
    primary: {
      '--card-bg': 'var(--primary)',
      '--card-text': 'var(--primary-foreground)',
      '--card-border': 'var(--primary)',
    },
    secondary: {
      '--card-bg': 'var(--secondary)',
      '--card-text': 'var(--secondary-foreground)',
      '--card-border': 'var(--secondary)',
    },
  };

  return (
    <div
      style={variantStyles[variant]}
      className="bg-[var(--card-bg)] text-[var(--card-text)] border-[var(--card-border)] border rounded-lg p-4"
    >
      {children}
    </div>
  );
}
```

#### 现代 CSS 特性应用

- **CSS Grid 子网格**：使用 `subgrid` 实现复杂布局对齐
- **CSS 逻辑属性**：使用 `margin-inline-start` 等逻辑属性
- **CSS 层叠层**：使用 `@layer` 管理样式优先级

```typescript
// 现代 CSS 特性示例组件
function ModernLayoutCard({
  header,
  content,
  footer,
}: {
  header: React.ReactNode;
  content: React.ReactNode;
  footer?: React.ReactNode;
}) {
  return (
    <div
      className={cn(
        // 使用 CSS Grid
        'grid grid-rows-[auto_1fr_auto]',
        // 逻辑属性
        'gap-4 p-4',
        'border rounded-lg bg-card',
        // 现代 CSS 特性
        'container-type-inline-size',
        // 响应式高度
        'min-h-[200px] @[400px]:min-h-[250px]'
      )}
    >
      <header className="border-b border-border pb-3">{header}</header>

      <main className="overflow-auto">{content}</main>

      {footer && (
        <footer className="border-t border-border pt-3">{footer}</footer>
      )}
    </div>
  );
}
```

## 前端开发核心原则

### 代码质量和组织原则

#### 组件设计原则

- **单一职责**：每个组件只负责一个明确的功能
- **组件大小控制**：保持组件代码在 50 行以内
- **Props 接口设计**：使用 TypeScript 接口定义清晰的 Props
- **组合优于继承**：使用组合模式构建复杂组件

```typescript
// 良好的组件设计示例
interface AlertProps {
  variant?: 'default' | 'destructive' | 'warning' | 'success';
  title?: string;
  children: React.ReactNode;
  onClose?: () => void;
  className?: string;
}

function Alert({
  variant = 'default',
  title,
  children,
  onClose,
  className,
}: AlertProps) {
  const variants = {
    default: 'bg-background border-border text-foreground',
    destructive: 'bg-destructive/10 border-destructive/20 text-destructive',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    success: 'bg-green-50 border-green-200 text-green-800',
  };

  return (
    <div
      className={cn(
        'relative rounded-lg border p-4',
        variants[variant],
        className
      )}
    >
      {title && (
        <h5 className="mb-1 font-medium leading-none tracking-tight">
          {title}
        </h5>
      )}
      <div className="text-sm opacity-90">{children}</div>
      {onClose && (
        <button
          onClick={onClose}
          className="absolute right-2 top-2 rounded-sm opacity-70 hover:opacity-100"
          aria-label="关闭"
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  );
}
```

#### 文件组织结构

- **原子设计原则**：按照原子、分子、组织、模板、页面的层级组织
- **功能模块化**：相关功能的组件放在同一目录
- **索引文件**：使用 `index.ts` 文件简化导入路径

```typescript
// components/ui/index.ts - 统一导出
export { Button } from './button';
export { Input } from './input';
export { Card, CardHeader, CardContent, CardFooter } from './card';
export { Alert } from './alert';

// components/forms/index.ts - 表单相关组件
export { LoginForm } from './login-form';
export { RegisterForm } from './register-form';
export { ContactForm } from './contact-form';

// 使用示例
import { Button, Card, Alert } from '@/components/ui';
import { LoginForm } from '@/components/forms';
```

### 状态管理策略

#### 服务器状态 vs 客户端状态分离

- **服务器状态**：使用 TanStack Query 管理 API 数据、缓存、同步
- **客户端状态**：使用 React hooks 管理 UI 状态、表单状态
- **全局状态**：使用 Context API 或 Zustand 管理跨组件状态

```typescript
// 服务器状态管理
function useUserProfile(userId: string) {
  return useQuery({
    queryKey: ['user', userId],
    queryFn: async () => {
      const response = await fetch(`/api/users/${userId}`);
      if (!response.ok) throw new Error('Failed to fetch user');
      return response.json();
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  });
}

// 客户端状态管理
function useFormState<T>(initialState: T) {
  const [state, setState] = useState(initialState);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const updateField = useCallback(
    (field: keyof T, value: any) => {
      setState((prev) => ({ ...prev, [field]: value }));
      // 清除字段错误
      if (errors[field as string]) {
        setErrors((prev) => ({ ...prev, [field]: undefined }));
      }
    },
    [errors]
  );

  return {
    state,
    errors,
    isSubmitting,
    updateField,
    setErrors,
    setIsSubmitting,
  };
}

// 全局状态管理（使用 Context）
interface AppState {
  user: User | null;
  theme: 'light' | 'dark';
  notifications: Notification[];
}

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

function useAppState() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppState must be used within AppProvider');
  }
  return context;
}
```

#### 避免属性钻取（Prop Drilling）

- **Context API**：用于深层组件树的状态共享
- **组合模式**：通过组件组合减少 props 传递
- **状态提升**：将状态提升到最近的共同父组件

```typescript
// 使用组合模式避免 prop drilling
function UserCard({ userId }: { userId: string }) {
  const { data: user, isLoading } = useUserProfile(userId);

  if (isLoading) return <UserCardSkeleton />;
  if (!user) return <UserCardError />;

  return (
    <Card>
      <UserCardHeader user={user} />
      <UserCardContent user={user} />
      <UserCardActions user={user} />
    </Card>
  );
}

// 子组件直接接收需要的数据
function UserCardHeader({ user }: { user: User }) {
  return (
    <CardHeader>
      <h3 className="text-lg font-semibold">{user.name}</h3>
      <p className="text-sm text-muted-foreground">{user.email}</p>
    </CardHeader>
  );
}
```

### 错误处理机制

#### 分层错误处理策略

- **API 层错误**：统一的 HTTP 错误处理和重试机制
- **组件层错误**：使用 Error Boundaries 捕获渲染错误
- **用户层错误**：友好的错误消息和恢复建议

```typescript
// API 错误处理
class ApiError extends Error {
  constructor(message: string, public status: number, public code?: string) {
    super(message);
    this.name = 'ApiError';
  }
}

async function apiRequest<T>(url: string, options?: RequestInit): Promise<T> {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new ApiError(
        errorData.message || 'Request failed',
        response.status,
        errorData.code
      );
    }

    return response.json();
  } catch (error) {
    if (error instanceof ApiError) throw error;
    throw new ApiError('Network error', 0);
  }
}

// React Error Boundary
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<
  { children: ReactNode; fallback?: ComponentType<{ error: Error }> },
  ErrorBoundaryState
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    // 发送错误报告到监控服务
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return <FallbackComponent error={this.state.error!} />;
    }

    return this.props.children;
  }
}

// 用户友好的错误组件
function DefaultErrorFallback({ error }: { error: Error }) {
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
      <h2 className="text-lg font-semibold mb-2">出现了一些问题</h2>
      <p className="text-muted-foreground mb-4">
        {error.message || '应用程序遇到了意外错误'}
      </p>
      <Button onClick={() => window.location.reload()}>刷新页面</Button>
    </div>
  );
}
```

#### Toast 通知系统

- **统一的通知接口**：成功、错误、警告、信息通知
- **自动消失机制**：根据重要性设置不同的显示时长
- **用户交互**：允许用户手动关闭或执行相关操作

```typescript
// Toast 通知 Hook
interface ToastOptions {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive' | 'success' | 'warning';
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

function useToast() {
  const { toast } = useToastContext();

  return {
    toast: (options: ToastOptions) => toast(options),
    success: (message: string, options?: Omit<ToastOptions, 'variant'>) =>
      toast({ ...options, description: message, variant: 'success' }),
    error: (message: string, options?: Omit<ToastOptions, 'variant'>) =>
      toast({ ...options, description: message, variant: 'destructive' }),
    warning: (message: string, options?: Omit<ToastOptions, 'variant'>) =>
      toast({ ...options, description: message, variant: 'warning' }),
  };
}

// 使用示例
function UserActions({ userId }: { userId: string }) {
  const { toast } = useToast();
  const deleteUser = useMutation({
    mutationFn: (id: string) =>
      apiRequest(`/api/users/${id}`, { method: 'DELETE' }),
    onSuccess: () => {
      toast.success('用户删除成功');
    },
    onError: (error: ApiError) => {
      toast.error('删除失败', {
        description: error.message,
        action: {
          label: '重试',
          onClick: () => deleteUser.mutate(userId),
        },
      });
    },
  });

  return (
    <Button
      variant="destructive"
      onClick={() => deleteUser.mutate(userId)}
      disabled={deleteUser.isPending}
    >
      {deleteUser.isPending ? '删除中...' : '删除用户'}
    </Button>
  );
}
```

### 性能优化要求

#### 代码分割和懒加载

- **路由级分割**：使用 Next.js 自动代码分割
- **组件级分割**：对大型组件使用 `React.lazy`
- **第三方库分割**：按需导入第三方库功能

```typescript
// 路由级懒加载
const DashboardPage = lazy(() => import('@/app/(dashboard)/page'));
const SettingsPage = lazy(() => import('@/app/(dashboard)/settings/page'));

// 组件级懒加载
const HeavyChart = lazy(() => import('@/components/charts/heavy-chart'));

function Dashboard() {
  const [showChart, setShowChart] = useState(false);

  return (
    <div>
      <h1>Dashboard</h1>
      <Button onClick={() => setShowChart(true)}>显示图表</Button>

      {showChart && (
        <Suspense fallback={<ChartSkeleton />}>
          <HeavyChart />
        </Suspense>
      )}
    </div>
  );
}

// 第三方库按需导入
const DatePicker = lazy(() =>
  import('react-datepicker').then((module) => ({
    default: module.default,
  }))
);
```

#### React 性能优化

- **memo 使用**：对纯组件使用 `React.memo`
- **回调优化**：使用 `useCallback` 和 `useMemo` 优化重渲染
- **状态结构优化**：避免不必要的状态更新

```typescript
// 优化的列表组件
interface ListItemProps {
  item: ListItem;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
}

const ListItem = memo(function ListItem({
  item,
  onEdit,
  onDelete,
}: ListItemProps) {
  // 使用 useCallback 避免子组件重渲染
  const handleEdit = useCallback(() => {
    onEdit(item.id);
  }, [item.id, onEdit]);

  const handleDelete = useCallback(() => {
    onDelete(item.id);
  }, [item.id, onDelete]);

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div>
        <h3 className="font-medium">{item.title}</h3>
        <p className="text-sm text-muted-foreground">{item.description}</p>
      </div>
      <div className="flex gap-2">
        <Button size="sm" onClick={handleEdit}>
          编辑
        </Button>
        <Button size="sm" variant="destructive" onClick={handleDelete}>
          删除
        </Button>
      </div>
    </div>
  );
});

// 父组件优化
function ItemList({ items }: { items: ListItem[] }) {
  const [editingId, setEditingId] = useState<string | null>(null);

  // 使用 useCallback 避免子组件重渲染
  const handleEdit = useCallback((id: string) => {
    setEditingId(id);
  }, []);

  const handleDelete = useCallback((id: string) => {
    // 删除逻辑
  }, []);

  // 使用 useMemo 优化计算
  const sortedItems = useMemo(
    () => items.sort((a, b) => a.title.localeCompare(b.title)),
    [items]
  );

  return (
    <div className="space-y-4">
      {sortedItems.map((item) => (
        <ListItem
          key={item.id}
          item={item}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      ))}
    </div>
  );
}
```

### 安全性考虑

#### 输入验证和数据清理

- **服务器端验证**：所有用户输入必须在服务器端验证
- **XSS 防护**：正确转义和清理用户生成的内容
- **SQL 注入防护**：使用 Prisma 的参数化查询
- **CSRF 保护**：实施跨站请求伪造保护

```typescript
// 输入验证中间件
import { z } from 'zod';
import DOMPurify from 'isomorphic-dompurify';

// 通用验证模式
const commonSchemas = {
  id: z.string().uuid('无效的 ID 格式'),
  email: z.string().email('无效的邮箱格式').max(255),
  password: z
    .string()
    .min(8, '密码至少需要 8 个字符')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字'),
  name: z
    .string()
    .min(1, '姓名不能为空')
    .max(100, '姓名过长')
    .regex(/^[a-zA-Z\u4e00-\u9fa5\s]+$/, '姓名包含无效字符'),
};

// 内容清理函数
function sanitizeHtml(content: string): string {
  return DOMPurify.sanitize(content, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li'],
    ALLOWED_ATTR: [],
  });
}

// API 路由验证示例
export async function POST(request: NextRequest) {
  try {
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();

    // 验证输入数据
    const createPostSchema = z.object({
      title: z.string().min(1).max(200),
      content: z.string().min(1).max(10000),
      tags: z.array(z.string().max(50)).max(10),
    });

    const validatedData = createPostSchema.parse(body);

    // 清理 HTML 内容
    const sanitizedContent = sanitizeHtml(validatedData.content);

    const post = await prisma.post.create({
      data: {
        ...validatedData,
        content: sanitizedContent,
        authorId: userId,
      },
      select: {
        id: true,
        title: true,
        content: true,
        createdAt: true,
      },
    });

    return NextResponse.json({ data: post }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
```

#### 身份验证和授权

- **JWT 令牌管理**：安全的令牌存储和刷新机制
- **角色权限控制**：基于角色的访问控制（RBAC）
- **会话管理**：安全的会话处理和超时机制

```typescript
// 权限检查中间件
interface Permission {
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete';
}

function hasPermission(userRole: string, permission: Permission): boolean {
  const rolePermissions: Record<string, Permission[]> = {
    admin: [
      { resource: '*', action: 'create' },
      { resource: '*', action: 'read' },
      { resource: '*', action: 'update' },
      { resource: '*', action: 'delete' },
    ],
    moderator: [
      { resource: 'posts', action: 'read' },
      { resource: 'posts', action: 'update' },
      { resource: 'posts', action: 'delete' },
      { resource: 'users', action: 'read' },
    ],
    user: [
      { resource: 'posts', action: 'create' },
      { resource: 'posts', action: 'read' },
      { resource: 'own_posts', action: 'update' },
      { resource: 'own_posts', action: 'delete' },
    ],
  };

  const permissions = rolePermissions[userRole] || [];
  return permissions.some(
    (p) =>
      (p.resource === '*' || p.resource === permission.resource) &&
      p.action === permission.action
  );
}

// 权限检查 Hook
function usePermission(permission: Permission) {
  const { user } = useAuth();
  return user ? hasPermission(user.role, permission) : false;
}

// 受保护的组件
function ProtectedAction({
  permission,
  children,
  fallback,
}: {
  permission: Permission;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const hasAccess = usePermission(permission);

  if (!hasAccess) {
    return fallback || <div className="text-muted-foreground">无权限访问</div>;
  }

  return <>{children}</>;
}
```

### 测试和文档要求

#### 测试策略实施

- **单元测试**：测试纯函数和工具函数
- **组件测试**：测试组件渲染和交互
- **集成测试**：测试 API 端点和数据流
- **E2E 测试**：测试关键用户流程

```typescript
// 单元测试示例
import { describe, it, expect } from 'vitest';
import { sanitizeHtml, hasPermission } from '@/lib/utils';

describe('sanitizeHtml', () => {
  it('should remove dangerous scripts', () => {
    const input = '<p>Hello</p><script>alert("xss")</script>';
    const result = sanitizeHtml(input);
    expect(result).toBe('<p>Hello</p>');
  });

  it('should preserve allowed tags', () => {
    const input = '<p><strong>Bold</strong> and <em>italic</em></p>';
    const result = sanitizeHtml(input);
    expect(result).toBe(input);
  });
});

// 组件测试示例
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '@/components/ui/button';

describe('Button', () => {
  it('should render with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toHaveTextContent('Click me');
  });

  it('should call onClick when clicked', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);

    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should be disabled when disabled prop is true', () => {
    render(<Button disabled>Click me</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
  });
});

// API 测试示例
import { describe, it, expect, beforeEach } from 'vitest';
import { POST } from '@/app/api/posts/route';
import { NextRequest } from 'next/server';

describe('/api/posts', () => {
  beforeEach(() => {
    // 清理测试数据
  });

  it('should create a post with valid data', async () => {
    const request = new NextRequest('http://localhost/api/posts', {
      method: 'POST',
      body: JSON.stringify({
        title: 'Test Post',
        content: 'This is a test post',
        tags: ['test'],
      }),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(201);
    expect(data.data).toHaveProperty('id');
    expect(data.data.title).toBe('Test Post');
  });

  it('should return 400 for invalid data', async () => {
    const request = new NextRequest('http://localhost/api/posts', {
      method: 'POST',
      body: JSON.stringify({
        title: '', // 无效的空标题
        content: 'Content',
      }),
    });

    const response = await POST(request);
    expect(response.status).toBe(400);
  });
});
```

#### 响应式布局测试

- **断点测试**：测试不同屏幕尺寸下的布局
- **触摸交互测试**：测试移动设备上的手势操作
- **可访问性测试**：测试键盘导航和屏幕阅读器支持

```typescript
// 响应式测试示例
import { render, screen } from '@testing-library/react';
import { ResponsiveGrid } from '@/components/layout/responsive-grid';

describe('ResponsiveGrid', () => {
  it('should render single column on mobile', () => {
    // 模拟移动设备视口
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });

    render(
      <ResponsiveGrid>
        <div>Item 1</div>
        <div>Item 2</div>
      </ResponsiveGrid>
    );

    const grid = screen.getByRole('grid');
    expect(grid).toHaveClass('grid-cols-1');
  });

  it('should render multiple columns on desktop', () => {
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });

    render(
      <ResponsiveGrid>
        <div>Item 1</div>
        <div>Item 2</div>
      </ResponsiveGrid>
    );

    const grid = screen.getByRole('grid');
    expect(grid).toHaveClass('lg:grid-cols-3');
  });
});

// E2E 测试示例（Playwright）
import { test, expect } from '@playwright/test';

test.describe('User Authentication Flow', () => {
  test('should allow user to sign in and access dashboard', async ({
    page,
  }) => {
    // 访问登录页面
    await page.goto('/sign-in');

    // 填写登录表单
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="sign-in-button"]');

    // 验证重定向到仪表板
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('h1')).toContainText('Dashboard');
  });

  test('should show error for invalid credentials', async ({ page }) => {
    await page.goto('/sign-in');

    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'wrongpassword');
    await page.click('[data-testid="sign-in-button"]');

    await expect(page.locator('[data-testid="error-message"]')).toContainText(
      'Invalid credentials'
    );
  });
});
```

#### 文档化要求

- **组件文档**：使用 JSDoc 注释描述组件用途和 Props
- **API 文档**：详细的 API 端点文档和示例
- **README 文件**：项目设置和开发指南
- **变更日志**：记录重要的功能更新和修复

````typescript
/**
 * 用户资料卡片组件
 *
 * @description 显示用户基本信息的卡片组件，支持编辑和删除操作
 * @example
 * ```tsx
 * <UserProfileCard
 *   user={user}
 *   onEdit={handleEdit}
 *   onDelete={handleDelete}
 *   editable={true}
 * />
 * ```
 */
interface UserProfileCardProps {
  /** 用户数据对象 */
  user: User;
  /** 编辑按钮点击回调 */
  onEdit?: (userId: string) => void;
  /** 删除按钮点击回调 */
  onDelete?: (userId: string) => void;
  /** 是否显示编辑和删除按钮 */
  editable?: boolean;
  /** 额外的 CSS 类名 */
  className?: string;
}

/**
 * 用户资料卡片组件
 */
function UserProfileCard({
  user,
  onEdit,
  onDelete,
  editable = false,
  className,
}: UserProfileCardProps) {
  // 组件实现...
}
````

## 数据库与 API 层

### Prisma + PostgreSQL

- 使用 **Prisma Client 单例模式**防止连接问题
- 设计具有适当**关系和约束**的模式
- 使用 **@@map** 将 TypeScript camelCase 映射到数据库 snake_case
- 为频繁查询的字段实施适当的**索引**
- 使用 **select** 仅获取必需字段，最小化 include 使用
- 用适当的 **try-catch 块**优雅处理数据库错误

```typescript
// lib/prisma.ts
import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient();

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;
```

### PostgreSQL 优化

- 实施**连接池**以提高性能
- 使用**事务**处理复杂的多步骤操作
- 应用适当的**索引策略**
- 定期维护数据库健康

## TanStack Query v5 集成

### 查询模式

- 使用 **Query Client** 进行适当的全局配置
- 实施 **Query Keys 工厂模式**实现一致的缓存管理
- 根据数据波动性设置适当的 **staleTime** 和 **gcTime**
- 为变更使用**乐观更新**，适当的错误回滚
- 分离**服务器状态**（TanStack Query）和**客户端状态**（React hooks）

```typescript
// lib/query-keys.ts
export const queryKeys = {
  users: ['users'] as const,
  user: (id: string) => [...queryKeys.users, id] as const,
  userPosts: (userId: string) => [...queryKeys.users, userId, 'posts'] as const,
};

// 可重用查询选项
export const profileQueryOptions = {
  queryKey: ['profile'],
  queryFn: async (): Promise<Profile> => {
    const { data } = await axios.get('/api/profile');
    return data;
  },
  staleTime: 1000 * 60 * 5, // 5 minutes
};
```

## API 路由与验证

### Zod 验证

- 在**所有 API 端点**使用 Zod 进行输入验证
- 实施**一致的错误处理**和适当的 HTTP 状态码
- 为请求/响应对象使用适当的 **TypeScript 类型**
- 处理边缘情况并提供**有意义的错误消息**
- 实施**速率限制**和安全措施

```typescript
// app/api/users/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';

const createUserSchema = z.object({
  email: z.string().email(),
  name: z.string().min(1),
});

export async function POST(request: NextRequest) {
  try {
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createUserSchema.parse(body);

    const user = await prisma.user.create({
      data: validatedData,
      select: { id: true, email: true, name: true },
    });

    return NextResponse.json({ data: user }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
```

## 身份验证与授权

### Clerk 集成

- 使用 **Clerk 组件和 hooks** 进行身份验证流程
- 使用**中间件**实施适当的路由保护
- 在 Clerk 和数据库之间**同步用户数据**
- 适当处理**用户角色和权限**
- 使用带适当身份验证检查的 **Server Actions**

```typescript
// middleware.ts
import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';

const isProtectedRoute = createRouteMatcher(['/dashboard(.*)', '/admin(.*)']);

export default clerkMiddleware(async (auth, req) => {
  const { userId, redirectToSignIn } = await auth();
  if (!userId && isProtectedRoute(req)) {
    return redirectToSignIn();
  }
});

export const config = {
  matcher: ['/((?!.+\\.[\\w]+$|_next).*)', '/', '/(api|trpc)(.*)'],
};
```

## 项目结构

```
├── app/
│   ├── (auth)/              # 认证路由组
│   │   ├── sign-in/
│   │   └── sign-up/
│   ├── (dashboard)/         # 仪表板路由组
│   │   ├── users/
│   │   └── settings/
│   ├── api/                 # API 路由
│   │   ├── users/
│   │   └── webhooks/
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── ui/                  # Shadcn 组件
│   ├── forms/               # 表单组件
│   ├── layout/              # 布局组件
│   └── shared/              # 共享组件
├── lib/
│   ├── prisma.ts           # Prisma 客户端
│   ├── utils.ts            # 工具函数
│   ├── schemas.ts          # Zod 模式
│   ├── query-keys.ts       # TanStack Query 键
│   └── constants.ts        # 应用常量
├── hooks/                   # 自定义 React hooks
├── types/                   # TypeScript 类型定义
├── prisma/
│   └── schema.prisma
└── middleware.ts            # Clerk 中间件
```

## 错误处理与加载状态

### 错误边界与状态

- 为路由级错误处理实施适当的 **error.tsx** 文件
- 使用 **React Error Boundaries** 进行组件级错误捕获
- 提供**有意义的错误消息**和恢复选项
- 使用 **Suspense 边界**实施适当的加载状态
- 使用**骨架组件**获得更好的感知性能

### 表单处理

- 使用 **react-hook-form** 与 Zod 验证实现类型安全表单
- 实施适当的**表单提交状态**和错误处理
- 为用户提供**实时验证反馈**
- 使用**重试机制**优雅处理网络错误

## MVP 开发指导原则

### 功能优先级

- **核心功能**：用户身份验证、基本 CRUD 操作
- **必要功能**：数据验证、错误处理、基本 UI
- **增强功能**：加载状态、动画、高级过滤
- **未来功能**：高级搜索、批量操作、分析

### 代码质量标准

- 编写**自文档化的代码**，使用清晰的变量和函数名
- 为复杂函数和组件添加 **JSDoc 注释**
- 为所有数据结构实施适当的 **TypeScript 类型**
- 使用 **ESLint 和 Prettier** 保持一致的代码格式
- 实施适当的 **git 提交约定**

### 性能考虑

- **避免过早优化**，专注于功能完整性
- 使用 **React DevTools** 和 **Next.js 分析工具**识别瓶颈
- 实施**代码分割**和**懒加载**策略
- 优化**图像和静态资源**
- 监控**包大小**和**运行时性能**

## 包管理最佳实践

### pnpm 使用规范

- **始终使用 pnpm** 进行依赖管理
- 使用 **pnpm add/remove** 而非手动编辑 package.json
- 利用 **pnpm workspace** 进行 monorepo 管理
- 定期运行 **pnpm audit** 检查安全漏洞
- 使用 **pnpm dlx** 运行一次性命令

```bash
# 推荐的包管理命令
pnpm add package-name
pnpm add -D package-name  # 开发依赖
pnpm remove package-name
pnpm update package-name
```

## 安全最佳实践

### 数据保护

- 使用 **环境变量** 存储敏感信息
- 实施适当的 **CORS 策略**
- 使用 **HTTPS** 进行所有生产通信
- 实施 **CSP（内容安全策略）**
- 定期更新依赖项以修复安全漏洞

### 输入验证

- **服务器端验证** 所有用户输入
- 使用 **Zod** 进行类型安全的数据验证
- 实施适当的 **SQL 注入防护**（Prisma 自动处理）
- 防范 **XSS 攻击**，正确转义用户内容

## 测试策略

### 测试层次

- **单元测试**：使用 Jest 和 React Testing Library
- **集成测试**：测试组件间交互
- **E2E 测试**：使用 Playwright 进行关键用户流程测试
- **类型检查**：利用 TypeScript 编译器进行静态分析

### 测试最佳实践

- 编写**可读性强的测试**，使用描述性测试名称
- 遵循 **AAA 模式**（Arrange、Act、Assert）
- 使用 **测试数据工厂** 创建一致的测试数据
- 实施 **快照测试** 防止意外的 UI 变更

## 部署与监控

### Vercel 部署优化

- 配置适当的 **环境变量**
- 使用 **Vercel Analytics** 监控性能
- 实施 **预览部署** 进行功能测试
- 配置 **自定义域名** 和 **SSL 证书**

## 开发工作流

### Git 工作流

- 使用 **功能分支** 进行开发
- 编写 **清晰的提交消息**
- 使用 **Pull Request** 进行代码审查
- 实施 **自动化 CI/CD** 流程
- 使用 **Conventional Commits** 规范提交信息, 不包含 emoji 和 optional 的 scope

### 代码审查标准

- 检查 **类型安全** 和错误处理
- 验证 **性能影响** 和最佳实践遵循
- 确保 **可访问性** 和用户体验
- 验证 **安全性** 和数据保护措施

---

## 总结

这个 AI 指导规则专注于构建高质量、可维护的 MVP 应用程序。通过遵循这些最佳实践，可以确保代码质量、性能和可扩展性，同时保持开发效率和团队协作的顺畅。

记住：**简单胜过复杂，可工作的代码胜过完美的架构**。在 MVP 阶段，优先考虑功能完整性和用户体验，然后再进行优化和重构。

```

```
