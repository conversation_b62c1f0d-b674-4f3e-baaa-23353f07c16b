# Next.js 15 MVP 全栈开发 AI 指导规则

基于最新技术最佳实践，专为快速高效的 MVP 开发而设计的 AI 助手指导规则。

## 核心开发理念

你是 TypeScript、Next.js 15、React 19、Shadcn UI、Tailwind CSS v4、Prisma、PostgreSQL、TanStack Query v5、Zod、pnpm 和 Clerk 的专家。

### 开发原则

- **MVP 优先**：快速迭代，保持必要的可扩展性，避免过度工程化
- **严格遵循 SOLID、KISS、DRY、YAGNI 原则**
- **函数式和声明式编程模式**优于命令式
- **组件驱动开发**，注重组合和复用
- **类型安全**贯穿整个技术栈
- **性能优先**，但不牺牲开发效率

## 代码风格与结构

### 通用准则

- 编写简洁的 TypeScript 代码，提供准确示例
- 使用函数式和声明式编程模式，避免类
- 优先迭代和模块化，避免代码重复
- 使用描述性变量名，带辅助动词（如 isLoading、hasError）
- 文件结构：导出组件、子组件、工具函数、静态内容、类型
- 使用 tabs 缩进，字符串用单引号
- 省略分号，除非消歧需要
- 始终使用严格相等（===）而非宽松相等（==）
- 行长度限制在 80 字符内

### 命名规范

- **组件、类型定义、接口**：PascalCase
- **目录**：小写连字符（如 components/auth-wizard）
- **文件名**：kebab-case（如 user-profile.tsx）
- **变量、函数、方法、Hooks、属性**：camelCase
- **环境变量、常量、全局配置**：UPPERCASE
- **事件处理器**前缀 'handle'：handleClick、handleSubmit
- **布尔变量**前缀动词：isLoading、hasError、canSubmit
- **自定义 Hooks** 前缀 'use'：useAuth、useApiCall
- **优先使用命名导出**

## TypeScript 最佳实践

### 核心实现

- 所有代码使用 TypeScript，**接口优于类型**
- 启用 tsconfig.json 中的**严格模式**
- **避免枚举**，使用 const 对象或联合类型
- 使用**类型守卫**安全处理 undefined 或 null 值
- 应用**泛型**提供类型灵活性
- 利用 TypeScript **工具类型**（Partial、Pick、Omit、Record）
- 使用**映射类型**动态创建现有类型的变体

```typescript
// 推荐：接口用于对象结构
interface UserProps {
  id: string;
  email: string;
  name: string;
}

// 推荐：const 对象替代枚举
const UserRole = {
  ADMIN: 'admin',
  USER: 'user',
  GUEST: 'guest',
} as const;

type UserRoleType = (typeof UserRole)[keyof typeof UserRole];
```

## Next.js 15 最佳实践

### 核心架构

- **专门使用 App Router**，避免 Pages Router
- **默认使用 Server Components** 提升性能
- **谨慎使用 'use client'**，仅在必要时：
  - 事件监听器和浏览器 API
  - 状态管理 hooks（useState、useReducer）
  - 仅客户端库
  - 需要用户输入的交互组件
- 使用 **generateMetadata** 实现适当的元数据管理
- 使用**路由组**实现基于文件的路由

### 性能优化

- **最小化** 'use client'、'useEffect' 和 'setState' 使用
- 优先使用 **React Server Components**
- 用 **Suspense** 包装客户端组件，提供回退 UI
- 对非关键组件使用**动态加载**
- 优化图像：使用 **Next.js Image** 组件、WebP 格式、懒加载
- 实施适当的**缓存策略**和重新验证
- 利用 Next.js 15 改进的**缓存机制**

### 新特性应用

- 利用 **React 19 支持**和新的 React 编译器
- 使用**部分预渲染（PPR）**结合 SSG 和 SSR
- 应用 **TurboPack 集成**提升开发速度

## React 19 最佳实践

### 组件架构

- 使用带 TypeScript 接口的**函数组件**
- 使用 **function 关键字**定义纯函数组件
- 将可重用逻辑提取到**自定义 hooks**
- 实现适当的**组件组合模式**
- 战略性使用 **React.memo()** 进行性能优化
- 在 useEffect hooks 中实施适当的**清理**

```typescript
interface ButtonProps {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
}

function Button({
  variant = 'primary',
  size = 'md',
  children,
  onClick,
  disabled = false,
}: ButtonProps) {
  return (
    <button
      className={cn(buttonVariants({ variant, size }))}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}
```

## Shadcn UI + Tailwind CSS v4 最佳实践

### 设计系统集成

- **严格使用项目提供的 CSS 变量**，不自定义颜色和圆角
- 所有组件必须使用 `index.css` 中定义的设计令牌：
  - 颜色：`--primary`、`--secondary`、`--muted` 等
  - 圆角：`--radius-sm`、`--radius-md`、`--radius-lg` 等
  - 阴影：`--shadow-sm`、`--shadow-md`、`--shadow-lg` 等
  - 字体：`--font-sans`、`--font-mono`、`--font-serif`

### 组件开发规范

- 使用 **Shadcn UI 组件**作为一致设计的基础
- 实施 **Radix UI 原语**以获得高级可访问性功能
- 应用**组合模式**创建模块化、可重用的组件
- 使用 **Tailwind 工具类**遵循移动优先的响应式设计
- 逻辑分组 Tailwind 类：布局、间距、颜色、排版
- 使用 **cn() 工具函数**进行条件类合并
- 使用 **next-themes** 实施暗模式支持

```typescript
// 正确：使用设计令牌
<div className="bg-primary text-primary-foreground rounded-lg shadow-md">
  Content
</div>

// 错误：自定义颜色和样式
<div className="bg-blue-500 text-white rounded-xl shadow-2xl">
  Content
</div>
```

### 响应式设计

- 使用 Tailwind 断点设计**移动优先**方法
- 确保可访问性合规的适当**对比度比**
- 使用**语义 HTML 元素**以获得更好的屏幕阅读器支持
- 为键盘导航实施适当的**焦点管理**

## 数据库与 API 层

### Prisma + PostgreSQL

- 使用 **Prisma Client 单例模式**防止连接问题
- 设计具有适当**关系和约束**的模式
- 使用 **@@map** 将 TypeScript camelCase 映射到数据库 snake_case
- 为频繁查询的字段实施适当的**索引**
- 使用 **select** 仅获取必需字段，最小化 include 使用
- 用适当的 **try-catch 块**优雅处理数据库错误

```typescript
// lib/prisma.ts
import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient();

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;
```

### PostgreSQL 优化

- 实施**连接池**以提高性能
- 使用**事务**处理复杂的多步骤操作
- 应用适当的**索引策略**
- 定期维护数据库健康

## TanStack Query v5 集成

### 查询模式

- 使用 **Query Client** 进行适当的全局配置
- 实施 **Query Keys 工厂模式**实现一致的缓存管理
- 根据数据波动性设置适当的 **staleTime** 和 **gcTime**
- 为变更使用**乐观更新**，适当的错误回滚
- 分离**服务器状态**（TanStack Query）和**客户端状态**（React hooks）

```typescript
// lib/query-keys.ts
export const queryKeys = {
  users: ['users'] as const,
  user: (id: string) => [...queryKeys.users, id] as const,
  userPosts: (userId: string) => [...queryKeys.users, userId, 'posts'] as const,
};

// 可重用查询选项
export const profileQueryOptions = {
  queryKey: ['profile'],
  queryFn: async (): Promise<Profile> => {
    const { data } = await axios.get('/api/profile');
    return data;
  },
  staleTime: 1000 * 60 * 5, // 5 minutes
};
```

## API 路由与验证

### Zod 验证

- 在**所有 API 端点**使用 Zod 进行输入验证
- 实施**一致的错误处理**和适当的 HTTP 状态码
- 为请求/响应对象使用适当的 **TypeScript 类型**
- 处理边缘情况并提供**有意义的错误消息**
- 实施**速率限制**和安全措施

```typescript
// app/api/users/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';

const createUserSchema = z.object({
  email: z.string().email(),
  name: z.string().min(1),
});

export async function POST(request: NextRequest) {
  try {
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createUserSchema.parse(body);

    const user = await prisma.user.create({
      data: validatedData,
      select: { id: true, email: true, name: true },
    });

    return NextResponse.json({ data: user }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
```

## 身份验证与授权

### Clerk 集成

- 使用 **Clerk 组件和 hooks** 进行身份验证流程
- 使用**中间件**实施适当的路由保护
- 在 Clerk 和数据库之间**同步用户数据**
- 适当处理**用户角色和权限**
- 使用带适当身份验证检查的 **Server Actions**

```typescript
// middleware.ts
import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';

const isProtectedRoute = createRouteMatcher(['/dashboard(.*)', '/admin(.*)']);

export default clerkMiddleware(async (auth, req) => {
  const { userId, redirectToSignIn } = await auth();
  if (!userId && isProtectedRoute(req)) {
    return redirectToSignIn();
  }
});

export const config = {
  matcher: ['/((?!.+\\.[\\w]+$|_next).*)', '/', '/(api|trpc)(.*)'],
};
```

## 项目结构

```
├── app/
│   ├── (auth)/              # 认证路由组
│   │   ├── sign-in/
│   │   └── sign-up/
│   ├── (dashboard)/         # 仪表板路由组
│   │   ├── users/
│   │   └── settings/
│   ├── api/                 # API 路由
│   │   ├── users/
│   │   └── webhooks/
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── ui/                  # Shadcn 组件
│   ├── forms/               # 表单组件
│   ├── layout/              # 布局组件
│   └── shared/              # 共享组件
├── lib/
│   ├── prisma.ts           # Prisma 客户端
│   ├── utils.ts            # 工具函数
│   ├── schemas.ts          # Zod 模式
│   ├── query-keys.ts       # TanStack Query 键
│   └── constants.ts        # 应用常量
├── hooks/                   # 自定义 React hooks
├── types/                   # TypeScript 类型定义
├── prisma/
│   └── schema.prisma
└── middleware.ts            # Clerk 中间件
```

## 错误处理与加载状态

### 错误边界与状态

- 为路由级错误处理实施适当的 **error.tsx** 文件
- 使用 **React Error Boundaries** 进行组件级错误捕获
- 提供**有意义的错误消息**和恢复选项
- 使用 **Suspense 边界**实施适当的加载状态
- 使用**骨架组件**获得更好的感知性能

### 表单处理

- 使用 **react-hook-form** 与 Zod 验证实现类型安全表单
- 实施适当的**表单提交状态**和错误处理
- 为用户提供**实时验证反馈**
- 使用**重试机制**优雅处理网络错误

## MVP 开发指导原则

### 功能优先级

- **核心功能**：用户身份验证、基本 CRUD 操作
- **必要功能**：数据验证、错误处理、基本 UI
- **增强功能**：加载状态、动画、高级过滤
- **未来功能**：高级搜索、批量操作、分析

### 代码质量标准

- 编写**自文档化的代码**，使用清晰的变量和函数名
- 为复杂函数和组件添加 **JSDoc 注释**
- 为所有数据结构实施适当的 **TypeScript 类型**
- 使用 **ESLint 和 Prettier** 保持一致的代码格式
- 实施适当的 **git 提交约定**

### 性能考虑

- **避免过早优化**，专注于功能完整性
- 使用 **React DevTools** 和 **Next.js 分析工具**识别瓶颈
- 实施**代码分割**和**懒加载**策略
- 优化**图像和静态资源**
- 监控**包大小**和**运行时性能**

## 包管理最佳实践

### pnpm 使用规范

- **始终使用 pnpm** 进行依赖管理
- 使用 **pnpm add/remove** 而非手动编辑 package.json
- 利用 **pnpm workspace** 进行 monorepo 管理
- 定期运行 **pnpm audit** 检查安全漏洞
- 使用 **pnpm dlx** 运行一次性命令

```bash
# 推荐的包管理命令
pnpm add package-name
pnpm add -D package-name  # 开发依赖
pnpm remove package-name
pnpm update package-name
```

## 安全最佳实践

### 数据保护

- 使用 **环境变量** 存储敏感信息
- 实施适当的 **CORS 策略**
- 使用 **HTTPS** 进行所有生产通信
- 实施 **CSP（内容安全策略）**
- 定期更新依赖项以修复安全漏洞

### 输入验证

- **服务器端验证** 所有用户输入
- 使用 **Zod** 进行类型安全的数据验证
- 实施适当的 **SQL 注入防护**（Prisma 自动处理）
- 防范 **XSS 攻击**，正确转义用户内容

## 测试策略

### 测试层次

- **单元测试**：使用 Jest 和 React Testing Library
- **集成测试**：测试组件间交互
- **E2E 测试**：使用 Playwright 进行关键用户流程测试
- **类型检查**：利用 TypeScript 编译器进行静态分析

### 测试最佳实践

- 编写**可读性强的测试**，使用描述性测试名称
- 遵循 **AAA 模式**（Arrange、Act、Assert）
- 使用 **测试数据工厂** 创建一致的测试数据
- 实施 **快照测试** 防止意外的 UI 变更

## 部署与监控

### Vercel 部署优化

- 配置适当的 **环境变量**
- 使用 **Vercel Analytics** 监控性能
- 实施 **预览部署** 进行功能测试
- 配置 **自定义域名** 和 **SSL 证书**

## 开发工作流

### Git 工作流

- 使用 **功能分支** 进行开发
- 编写 **清晰的提交消息**
- 使用 **Pull Request** 进行代码审查
- 实施 **自动化 CI/CD** 流程
- 使用 **Conventional Commits** 规范提交信息, 不包含 emoji 和 optional 的 scope

### 代码审查标准

- 检查 **类型安全** 和错误处理
- 验证 **性能影响** 和最佳实践遵循
- 确保 **可访问性** 和用户体验
- 验证 **安全性** 和数据保护措施

---

## 总结

这个 AI 指导规则专注于构建高质量、可维护的 MVP 应用程序。通过遵循这些最佳实践，可以确保代码质量、性能和可扩展性，同时保持开发效率和团队协作的顺畅。

记住：**简单胜过复杂，可工作的代码胜过完美的架构**。在 MVP 阶段，优先考虑功能完整性和用户体验，然后再进行优化和重构。

```

```
